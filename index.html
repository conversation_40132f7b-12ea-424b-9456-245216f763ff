<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="icon" type="image/png" href="../Portofolio_V5-main/dist/hamas.png"  />

  <!-- Primary Meta Tags -->
  <title><PERSON> – Front-End Web Developer</title>
  <meta name="title" content="<PERSON><PERSON> – Front-End Web Developer" />
  <meta name="description" content="<PERSON><PERSON><PERSON>, pengembang web front-end yang berfokus pada desain antarmuka modern, responsif, dan performa tinggi. Lihat portofolio dan kontak saya di sini." />
  <meta name="keywords" content="<PERSON><PERSON>, <PERSON><PERSON>, Front-End Developer, Web Developer Indonesia, Portofolio Eki, ekizr" />
  <meta name="google-site-verification" content="ZYNfZwMUiNwSMF3EMvY85bid2BVvB12uMMRfUNWw75A" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="theme-color" content="#030014" />
  <meta name="msapplication-navbutton-color" content="#030014" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://eki.my.id/" />
  <meta property="og:title" content="Eki Zulfar Rachman – Front-End Web Developer" />
  <meta property="og:description" content="Portofolio Eki Zulfar Rachman – pengembang web dengan fokus pada desain modern dan performa tinggi." />
  <meta property="og:image" content="https://eki.my.id/Meta.png" />

  <!-- Twitter Meta Tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:url" content="https://eki.my.id/" />
  <meta name="twitter:title" content="Eki Zulfar Rachman – Front-End Web Developer" />
  <meta name="twitter:description" content="Portofolio Eki Zulfar Rachman – pengembang web dengan fokus pada desain modern dan performa tinggi." />
  <meta name="twitter:image" content="https://eki.my.id/Meta.png" />

  <!-- Structured Data JSON-LD -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "itemListElement": [
      {
        "@type": "SiteNavigationElement",
        "name": "Home",
        "url": "https://www.eki.my.id/#Home"
      },
      {
        "@type": "SiteNavigationElement",
        "name": "About",
        "url": "https://www.eki.my.id/#About"
      },
      {
        "@type": "SiteNavigationElement",
        "name": "Portofolio",
        "url": "https://www.eki.my.id/#Portofolio"
      },
      {
        "@type": "SiteNavigationElement",
        "name": "Contact",
        "url": "https://www.eki.my.id/#Contact"
      }
    ]
  }
  </script>
</head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  

  </body>
</html>
