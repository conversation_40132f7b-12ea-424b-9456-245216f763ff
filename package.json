{"name": "portofolio-v5", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@babel/runtime": "^7.21.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^1.0.6", "@lottiefiles/dotlottie-react": "^0.11.0", "@mui/icons-material": "^6.1.6", "@mui/material": "^6.1.6", "@mui/styled-engine-sc": "^6.1.6", "@react-spring/web": "^9.7.5", "@shadcn/ui": "^0.0.4", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.37", "@supabase/supabase-js": "^2.49.9", "add": "^2.0.6", "aos": "^2.3.4", "axios": "^1.10.0", "clsx": "^2.1.1", "dialog": "^0.3.1", "firebase": "^11.0.1", "framer-motion": "^11.15.0", "gsap": "^3.12.5", "lucide-react": "^0.454.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.13.1", "react-router-dom": "^6.28.0", "react-swipeable-views": "^0.14.0", "react-swipeable-views-utils": "^0.14.0", "styled-components": "^6.1.13", "sweetalert2": "^11.15.0", "tailwind": "^4.0.0", "tailwind-merge": "^2.5.5", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.21", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.5.6", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "vite": "^5.4.10"}, "overrides": {"react-event-listener": {"react": "^18.2.0"}, "react-swipeable-views": {"react": "^18.2.0"}}}